#!/bin/bash

# Test script for group event notifications functionality
# This script tests that group members receive notifications when events are created

echo "🧪 Testing Group Event Notifications Functionality"
echo "=================================================="

# Configuration
BASE_URL="http://localhost:8080/api"
GROUP_ID="eac84ed5-a7e4-4179-8c5d-1826106f183a"
CREATOR_TOKEN="a468bc67-ff87-4a69-9633-6399daa0bf78"  # ben's token
MEMBER1_TOKEN="8acebb14-7861-46ff-adc4-75dc3428331f"  # poche's token
MEMBER2_TOKEN="e3de5275-9b51-4c06-940d-369d0b414dcb"  # test's token

# Function to create an event
create_event() {
    local title="$1"
    local description="$2"
    
    echo "📅 Creating event: $title"
    
    response=$(curl -s -X POST "$BASE_URL/groups/$GROUP_ID/events" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $CREATOR_TOKEN" \
        -d "{
            \"title\": \"$title\",
            \"description\": \"$description\",
            \"location\": \"Test Location\",
            \"startTime\": \"2025-06-25T10:00:00Z\",
            \"endTime\": \"2025-06-25T12:00:00Z\"
        }")
    
    if echo "$response" | grep -q '"success":true'; then
        echo "✅ Event created successfully"
        event_id=$(echo "$response" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4)
        echo "   Event ID: $event_id"
        return 0
    else
        echo "❌ Failed to create event"
        echo "   Response: $response"
        return 1
    fi
}

# Function to check notifications for a user
check_notifications() {
    local user_name="$1"
    local token="$2"
    local expected_event_title="$3"
    
    echo "🔔 Checking notifications for $user_name"
    
    response=$(curl -s -X GET "$BASE_URL/notifications" \
        -H "Authorization: Bearer $token")
    
    if echo "$response" | grep -q '"success":true'; then
        # Check if there's a group_event_created notification
        if echo "$response" | grep -q '"type":"group_event_created"'; then
            # Check if the event title matches (accounting for JSON escaping)
            if echo "$response" | grep -q "eventTitle.*$expected_event_title"; then
                echo "✅ $user_name received event notification correctly"
                echo "   Event title found: $expected_event_title"
                return 0
            else
                echo "⚠️  $user_name received notification but event title doesn't match"
                echo "   Expected: $expected_event_title"
                echo "   Response: $(echo "$response" | grep -o '"data":"[^"]*"' | head -1)"
                return 1
            fi
        else
            echo "❌ $user_name did not receive event notification"
            return 1
        fi
    else
        echo "❌ Failed to get notifications for $user_name"
        echo "   Response: $response"
        return 1
    fi
}

# Main test execution
echo ""
echo "🚀 Starting test..."
echo ""

# Test 1: Create an event and check notifications
EVENT_TITLE="Automated Test Event $(date +%s)"
EVENT_DESCRIPTION="This event was created by the automated test script"

if create_event "$EVENT_TITLE" "$EVENT_DESCRIPTION"; then
    echo ""
    echo "⏳ Waiting 2 seconds for notifications to be processed..."
    sleep 2
    
    echo ""
    echo "🔍 Checking if group members received notifications..."
    
    # Check notifications for member 1 (poche)
    check_notifications "poche" "$MEMBER1_TOKEN" "$EVENT_TITLE"
    member1_result=$?
    
    echo ""
    
    # Check notifications for member 2 (test)
    check_notifications "test" "$MEMBER2_TOKEN" "$EVENT_TITLE"
    member2_result=$?
    
    echo ""
    echo "📊 Test Results:"
    echo "==============="
    
    if [ $member1_result -eq 0 ] && [ $member2_result -eq 0 ]; then
        echo "🎉 ALL TESTS PASSED!"
        echo "   ✅ Event created successfully"
        echo "   ✅ All group members received notifications"
        echo "   ✅ Notifications contain correct event information"
        exit 0
    else
        echo "❌ SOME TESTS FAILED!"
        [ $member1_result -ne 0 ] && echo "   ❌ poche did not receive correct notification"
        [ $member2_result -ne 0 ] && echo "   ❌ test did not receive correct notification"
        exit 1
    fi
else
    echo ""
    echo "❌ TEST FAILED: Could not create event"
    exit 1
fi
